@extends('layouts.vendor')

@section('page-title', 'Dashboard')

@section('dashboard-content')
<div class="mb-8">
    <!-- Welcome Card -->
    <div class="card overflow-hidden">
        <div class="flex flex-col md:flex-row items-center justify-between p-6 md:p-8">
            <div class="md:w-2/3 mb-6 md:mb-0">
                <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200 mb-4">
                    <i class="fas fa-crown mr-2"></i>
                    Premium Vendor
                </div>
                <h1 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    Welcome back, <span class="text-primary-600 dark:text-primary-400">{{ Auth::user()->name }}</span>!
                </h1>
                <p class="text-gray-600 dark:text-gray-300">
                    Track your sales, orders, and customers in real time. Grow your business with WhaMart.
                </p>
                <div class="mt-6 flex flex-wrap gap-3">
                    <a href="#" class="btn btn-primary">
                        <i class="fas fa-plus mr-2"></i> Add New Product
                    </a>
                    <a href="#" class="btn btn-outline">
                        <i class="fas fa-chart-line mr-2"></i> View Analytics
                    </a>
                </div>
            </div>
            <div class="hidden md:block md:w-1/3">
                <img src="{{ asset('images/dashboard-illustration.svg') }}" alt="Dashboard" class="w-full h-auto">
            </div>
        </div>
    </div>
</div>

<!-- Stats Grid -->
<div class="stats-grid mb-8">
    <!-- Revenue Card -->
    <div class="stat-card group">
        <div class="flex items-center justify-between">
            <div>
                <p class="stat-card-title">Today's Revenue</p>
                <h3 class="stat-card-value">₹45,280</h3>
                <span class="stat-card-change positive">
                    <i class="fas fa-arrow-up mr-1"></i> 12.5%
                </span>
            </div>
            <div class="p-3 rounded-full bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-300">
                <i class="fas fa-rupee-sign text-2xl"></i>
            </div>
        </div>
    </div>

    <!-- Orders Card -->
    <div class="stat-card group">
        <div class="flex items-center justify-between">
            <div>
                <p class="stat-card-title">New Orders</p>
                <h3 class="stat-card-value">23</h3>
                <span class="stat-card-change positive">
                    <i class="fas fa-arrow-up mr-1"></i> 8.2%
                </span>
            </div>
            <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300">
                <i class="fas fa-shopping-cart text-2xl"></i>
            </div>
        </div>
    </div>

    <!-- Customers Card -->
    <div class="stat-card group">
        <div class="flex items-center justify-between">
            <div>
                <p class="stat-card-title">Active Customers</p>
                <h3 class="stat-card-value">156</h3>
                <span class="stat-card-change positive">
                    <i class="fas fa-arrow-up mr-1"></i> 5.3%
                </span>
            </div>
            <div class="p-3 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300">
                <i class="fas fa-users text-2xl"></i>
            </div>
        </div>
    </div>

    <!-- Products Card -->
    <div class="stat-card group">
        <div class="flex items-center justify-between">
            <div>
                <p class="stat-card-title">Total Products</p>
                <h3 class="stat-card-value">120</h3>
                <span class="stat-card-change negative">
                    <i class="fas fa-arrow-down mr-1"></i> 2.1%
                </span>
            </div>
            <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300">
                <i class="fas fa-box text-2xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- Recent Orders -->
<div class="card mb-8">
    <div class="card-header">
        <h2 class="card-title">Recent Orders</h2>
        <a href="#" class="text-sm font-medium text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-300">
            View All <i class="fas fa-arrow-right ml-1"></i>
        </a>
    </div>
    <div class="overflow-x-auto">
        <div class="table-container">
            <table class="w-full">
                <thead>
                    <tr>
                        <th>Order ID</th>
                        <th>Customer</th>
                        <th>Date</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <td class="font-medium text-gray-900 dark:text-white">#ORD-1289</td>
                        <td>Rahul Sharma</td>
                        <td>27 Jun 2025</td>
                        <td class="font-medium">₹1,899</td>
                        <td>
                            <span class="badge badge-success">
                                <i class="fas fa-check-circle mr-1"></i> Delivered
                            </span>
                        </td>
                        <td>
                            <button class="text-gray-500 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <td class="font-medium text-gray-900 dark:text-white">#ORD-1288</td>
                        <td>Priya Patel</td>
                        <td>27 Jun 2025</td>
                        <td class="font-medium">₹3,299</td>
                        <td>
                            <span class="badge badge-warning">
                                <i class="fas fa-sync-alt mr-1 animate-spin"></i> Processing
                            </span>
                        </td>
                        <td>
                            <button class="text-gray-500 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <td class="font-medium text-gray-900 dark:text-white">#ORD-1287</td>
                        <td>Vikram Singh</td>
                        <td>26 Jun 2025</td>
                        <td class="font-medium">₹5,499</td>
                        <td>
                            <span class="badge badge-info">
                                <i class="fas fa-truck mr-1"></i> Shipped
                            </span>
                        </td>
                        <td>
                            <button class="text-gray-500 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <td class="font-medium text-gray-900 dark:text-white">#ORD-1286</td>
                        <td>Ananya Gupta</td>
                        <td>26 Jun 2025</td>
                        <td class="font-medium">₹2,199</td>
                        <td>
                            <span class="badge badge-danger">
                                <i class="fas fa-times-circle mr-1"></i> Cancelled
                            </span>
                        </td>
                        <td>
                            <button class="text-gray-500 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
    <!-- Recent Activity -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">Recent Activity</h2>
        </div>
        <div class="space-y-4 p-4">
            @foreach([
                ['icon' => 'shopping-cart', 'color' => 'text-blue-500', 'text' => 'New order #1290 received', 'time' => '2 min ago'],
                ['icon' => 'user', 'color' => 'text-green-500', 'text' => 'New customer registered', 'time' => '1 hour ago'],
                ['icon' => 'star', 'color' => 'text-yellow-500', 'text' => 'Product review received', 'time' => '3 hours ago'],
                ['icon' => 'money-bill-wave', 'color' => 'text-purple-500', 'text' => 'Payment of ₹8,750 received', 'time' => '5 hours ago'],
                ['icon' => 'truck', 'color' => 'text-indigo-500', 'text' => 'Order #1285 has been shipped', 'time' => '1 day ago'],
            ] as $activity)
            <div class="flex items-start">
                <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center mr-3">
                    <i class="fas fa-{{ $activity['icon'] }} {{ $activity['color'] }}"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $activity['text'] }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ $activity['time'] }}</p>
                </div>
            </div>
            @endforeach
        </div>
    </div>

    <!-- Top Products -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">Top Selling Products</h2>
        </div>
        <div class="space-y-4 p-4">
            @foreach([
                ['name' => 'Wireless Earbuds', 'sales' => '1,245', 'revenue' => '₹1,24,500', 'change' => '12.5%', 'trend' => 'up'],
                ['name' => 'Smart Watch', 'sales' => '987', 'revenue' => '₹1,97,400', 'change' => '8.3%', 'trend' => 'up'],
                ['name' => 'Bluetooth Speaker', 'sales' => '756', 'revenue' => '₹1,13,400', 'change' => '5.7%', 'trend' => 'up'],
                ['name' => 'Power Bank', 'sales' => '543', 'revenue' => '₹81,450', 'change' => '2.1%', 'trend' => 'down'],
                ['name' => 'Phone Case', 'sales' => '321', 'revenue' => '₹16,050', 'change' => '1.2%', 'trend' => 'up'],
            ] as $product)
            <div class="flex items-center justify-between">
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 dark:text-white truncate">{{ $product['name'] }}</p>
                    <div class="flex items-center text-xs text-gray-500 dark:text-gray-400">
                        <span>{{ $product['sales'] }} sales</span>
                        <span class="mx-2">•</span>
                        <span>{{ $product['revenue'] }}</span>
                    </div>
                </div>
                <div class="ml-4 flex-shrink-0">
                    <span class="inline-flex items-center text-xs font-medium {{ $product['trend'] === 'up' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                        @if($product['trend'] === 'up')
                            <i class="fas fa-arrow-up mr-1"></i>
                        @else
                            <i class="fas fa-arrow-down mr-1"></i>
                        @endif
                        {{ $product['change'] }}
                    </span>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</div>

<!-- Performance Overview -->
<div class="card mb-8">
    <div class="card-header">
        <h2 class="card-title">Performance Overview</h2>
        <div class="flex items-center space-x-2">
            <button class="text-sm px-3 py-1 rounded-md bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-200">
                This Month
            </button>
            <button class="text-sm px-3 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300">
                Last Month
            </button>
            <button class="text-sm px-3 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300">
                This Year
            </button>
        </div>
    </div>
    <div class="p-6">
        <div class="h-80">
            <!-- Chart will be rendered by ApexCharts -->
            <div id="performanceChart"></div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize ApexCharts
        var options = {
            series: [{
                name: 'Revenue',
                type: 'column',
                data: [44000, 55000, 41000, 67000, 58000, 72000, 69000, 91000, 102000, 83000, 93000, 108000]
            }, {
                name: 'Orders',
                type: 'line',
                data: [300, 350, 280, 420, 380, 450, 430, 520, 590, 480, 540, 600]
            }],
            chart: {
                height: '100%',
                type: 'line',
                stacked: false,
                toolbar: {
                    show: true
                },
                zoom: {
                    enabled: true
                },
                fontFamily: 'Inter, sans-serif',
                foreColor: '#6b7280',
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                width: [1, 4],
                curve: 'smooth'
            },
            xaxis: {
                categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            },
            yaxis: [
                {
                    axisTicks: {
                        show: true,
                    },
                    axisBorder: {
                        show: true,
                        color: '#008FFB'
                    },
                    labels: {
                        style: {
                            colors: '#008FFB',
                        },
                        formatter: function(value) {
                            return '₹' + (value / 1000) + 'k';
                        }
                    },
                    title: {
                        text: "Revenue (₹)",
                        style: {
                            color: '#008FFB',
                        }
                    },
                },
                {
                    opposite: true,
                    axisTicks: {
                        show: true,
                    },
                    axisBorder: {
                        show: true,
                        color: '#00E396'
                    },
                    labels: {
                        style: {
                            colors: '#00E396',
                        }
                    },
                    title: {
                        text: "Orders",
                        style: {
                            color: '#00E396',
                        }
                    }
                },
            ],
            tooltip: {
                fixed: {
                    enabled: true,
                    position: 'topLeft',
                    offsetY: 30,
                    offsetX: 60
                },
            },
            legend: {
                horizontalAlign: 'left',
                offsetX: 40
            },
            colors: ['#3B82F6', '#10B981'],
            grid: {
                borderColor: '#f1f1f1',
                strokeDashArray: 5,
            }
        };

        var chart = new ApexCharts(document.querySelector("#performanceChart"), options);
        chart.render();

        // Update chart on window resize
        window.addEventListener('resize', function() {
            chart.updateOptions({
                chart: {
                    width: '100%'
                }
            });
        });
    });
</script>
@endpush

@endsection
