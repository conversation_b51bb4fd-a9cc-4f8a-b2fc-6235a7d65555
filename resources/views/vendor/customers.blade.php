@extends('layouts.vendor')

@section('content')
<div x-data="vendorCustomers()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Customer Management</h1>

    <template x-if="loading">
        <div class="flex justify-center items-center h-64"><div class="loader"></div></div>
    </template>

    <template x-if="!loading && stats">
        <div>
            <!-- Customer Stats -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white p-6 rounded-lg shadow-lg"><p class="text-gray-600 text-sm">Active Customers</p><p class="text-3xl font-bold text-gray-900 mt-2" x-text="stats.activeCount"></p></div>
                <div class="bg-white p-6 rounded-lg shadow-lg"><p class="text-gray-600 text-sm">Inactive Customers</p><p class="text-3xl font-bold text-gray-900 mt-2" x-text="stats.inactiveCount"></p></div>
                <div class="bg-white p-6 rounded-lg shadow-lg"><p class="text-gray-600 text-sm">Total Orders</p><p class="text-3xl font-bold text-gray-900 mt-2" x-text="stats.totalOrders"></p></div>
                <div class="bg-white p-6 rounded-lg shadow-lg"><p class="text-gray-600 text-sm">Total Revenue</p><p class="text-3xl font-bold text-gray-900 mt-2" x-text="formatCurrency(stats.totalRevenue)"></p></div>
            </div>

            <!-- Customer List -->
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-xl font-bold text-gray-800 mb-4">All Customers</h2>
                <div class="flex items-center justify-between mb-4">
                    <input type="text" x-model="searchTerm" placeholder="Search customers..." class="w-full md:w-1/3 px-4 py-2 border border-gray-300 rounded-lg">
                    <select x-model="statusFilter" class="ml-4 px-4 py-2 border border-gray-300 rounded-lg">
                        <option value="All">All Statuses</option><option value="Active">Active</option><option value="Inactive">Inactive</option>
                    </select>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50"><tr><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Name</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Total Spent</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Last Order</th></tr></thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <template x-for="customer in paginatedCustomers" :key="customer.id">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" x-text="customer.name"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" :class="customer.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" x-text="customer.status"></span></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="formatCurrency(customer.totalSpent)"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="customer.lastOrder"></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
                <!-- Pagination -->
                <div class="mt-4 flex justify-between items-center">
                    <p class="text-sm text-gray-700">Showing <span x-text="(currentPage - 1) * itemsPerPage + 1"></span> to <span x-text="Math.min(currentPage * itemsPerPage, filteredCustomers.length)"></span> of <span x-text="filteredCustomers.length"></span> results</p>
                    <div>
                        <button @click="currentPage = Math.max(1, currentPage - 1)" :disabled="currentPage === 1" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">Prev</button>
                        <button @click="currentPage = Math.min(totalPages, currentPage + 1)" :disabled="currentPage === totalPages" class="ml-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">Next</button>
                    </div>
                </div>
            </div>
        </div>
    </template>
</div>

<script>
function vendorCustomers() {
    return {
        loading: true,
        customers: [],
        stats: null,
        searchTerm: '',
        statusFilter: 'All',
        currentPage: 1,
        itemsPerPage: 10,

        init() {
            this.fetchData();
        },

        fetchData() {
            this.loading = true;
            setTimeout(() => {
                this.stats = { activeCount: 850, inactiveCount: 150, totalOrders: 5400, totalRevenue: 1250000 };
                this.customers = Array.from({ length: 1000 }, (_, i) => ({
                    id: i + 1, name: `Customer ${i + 1}`,
                    status: i % 7 === 0 ? 'Inactive' : 'Active',
                    totalSpent: Math.floor(500 + Math.random() * 5000),
                    lastOrder: new Date(2023, 0, i + 1).toISOString().split('T')[0]
                }));
                this.loading = false;
            }, 500);
        },

        get filteredCustomers() {
            return this.customers.filter(c => {
                const matchesSearch = c.name.toLowerCase().includes(this.searchTerm.toLowerCase());
                const matchesStatus = this.statusFilter === 'All' || c.status === this.statusFilter;
                return matchesSearch && matchesStatus;
            });
        },

        get totalPages() {
            return Math.ceil(this.filteredCustomers.length / this.itemsPerPage);
        },

        get paginatedCustomers() {
            const start = (this.currentPage - 1) * this.itemsPerPage;
            const end = start + this.itemsPerPage;
            return this.filteredCustomers.slice(start, end);
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR', maximumFractionDigits: 0 }).format(amount);
        }
    }
}
</script>
@endsection

