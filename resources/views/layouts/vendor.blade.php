<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" x-data="vendorLayout()" :class="{ 'dark': isDarkMode }">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Dashboard') - WhaMart Vendor</title>

    <!-- Modern Fonts & Icons -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js">

    <!-- Modern Vendor Dashboard CSS -->
    <link rel="stylesheet" href="{{ asset('css/vendor-layout-modern.css') }}">
    <link rel="stylesheet" href="{{ asset('css/vendor-components.css') }}">
    @stack('page-styles')

    <!-- Alpine.js -->
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

    <!-- Chart Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Animation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
</head>
<body class="vendor-body">

<!-- Loading Screen -->
<div x-show="isLoading" x-transition:leave="transition-opacity duration-500" class="vendor-loading-screen">
    <div class="loading-content">
        <div class="loading-logo">
            <img src="/WhaMart_Logo.png" alt="WhaMart" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
            <div class="loading-logo-fallback" style="display: none;">W</div>
        </div>
        <div class="loading-spinner"></div>
        <p class="loading-text">Loading your dashboard...</p>
    </div>
</div>

<!-- Main App Container -->
<div class="vendor-app" x-show="!isLoading" x-transition:enter="transition-opacity duration-500">
    <!-- Mobile Overlay -->
    <div x-show="isMobileSidebarOpen"
         @click="closeMobileSidebar()"
         class="vendor-mobile-overlay"
         x-transition:enter="transition-opacity ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0">
    </div>

    <!-- Modern Sidebar -->
    <aside class="vendor-sidebar"
           :class="{ 'sidebar-mobile-open': isMobileSidebarOpen, 'sidebar-collapsed': isSidebarCollapsed }"
           @click.away="if (window.innerWidth < 1024) closeMobileSidebar()">

        <!-- Sidebar Header -->
        <div class="sidebar-header">
            <div class="sidebar-brand" @click="navigateToHome()">
                <div class="brand-logo">
                    <img src="/WhaMart_Logo.png" alt="WhaMart" class="brand-logo-img"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="brand-logo-fallback" style="display: none;">
                        <i class="fas fa-store"></i>
                    </div>
                </div>
                <div class="brand-text" x-show="!isSidebarCollapsed">
                    <h1 class="brand-title">WhaMart</h1>
                    <p class="brand-subtitle">Vendor Portal</p>
                </div>
            </div>

            <!-- Sidebar Controls -->
            <div class="sidebar-controls">
                <button @click="toggleSidebarCollapse()" class="sidebar-toggle-btn desktop-only" title="Toggle Sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <button @click="closeMobileSidebar()" class="sidebar-close-btn mobile-only" title="Close Sidebar">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- Modern Navigation -->
        <nav class="sidebar-nav">
            <!-- Main Navigation -->
            <div class="nav-section">
                <div class="nav-section-title" x-show="!isSidebarCollapsed">
                    <span>Main Menu</span>
                </div>

                <div class="nav-items">
                    <a href="{{ route('vendor.dashboard') }}"
                       class="nav-item {{ request()->routeIs('vendor.dashboard') ? 'active' : '' }}"
                       title="Dashboard">
                        <div class="nav-item-icon">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <span class="nav-item-text" x-show="!isSidebarCollapsed">Dashboard</span>
                        <div class="nav-item-indicator"></div>
                    </a>

                    <a href="{{ route('vendor.products.index') }}"
                       class="nav-item {{ request()->routeIs('vendor.products.*') ? 'active' : '' }}"
                       title="Products">
                        <div class="nav-item-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <span class="nav-item-text" x-show="!isSidebarCollapsed">Products</span>
                        <div class="nav-item-indicator"></div>
                    </a>

                    <a href="{{ route('vendor.orders') }}"
                       class="nav-item {{ request()->routeIs('vendor.orders*') ? 'active' : '' }}"
                       title="Orders">
                        <div class="nav-item-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <span class="nav-item-text" x-show="!isSidebarCollapsed">Orders</span>
                        <span class="nav-item-badge" x-show="!isSidebarCollapsed">5</span>
                        <div class="nav-item-indicator"></div>
                    </a>

                    <a href="{{ route('vendor.customers') }}"
                       class="nav-item {{ request()->routeIs('vendor.customers*') ? 'active' : '' }}"
                       title="Customers">
                        <div class="nav-item-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <span class="nav-item-text" x-show="!isSidebarCollapsed">Customers</span>
                        <div class="nav-item-indicator"></div>
                    </a>

                    <a href="{{ route('vendor.analytics') }}"
                       class="nav-item {{ request()->routeIs('vendor.analytics*') ? 'active' : '' }}"
                       title="Analytics">
                        <div class="nav-item-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <span class="nav-item-text" x-show="!isSidebarCollapsed">Analytics</span>
                        <div class="nav-item-indicator"></div>
                    </a>
                </div>
            </div>

            <!-- Business Tools Section -->
            <div class="nav-section">
                <div class="nav-section-title" x-show="!isSidebarCollapsed">
                    <span>Business Tools</span>
                </div>

                <div class="nav-items">
                    <a href="{{ route('vendor.marketing') }}"
                       class="nav-item {{ request()->routeIs('vendor.marketing*') ? 'active' : '' }}"
                       title="Marketing">
                        <div class="nav-item-icon">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                        <span class="nav-item-text" x-show="!isSidebarCollapsed">Marketing</span>
                        <div class="nav-item-indicator"></div>
                    </a>

                    <a href="{{ route('vendor.reports') }}"
                       class="nav-item {{ request()->routeIs('vendor.reports*') ? 'active' : '' }}"
                       title="Reports">
                        <div class="nav-item-icon">
                            <i class="fas fa-file-chart-column"></i>
                        </div>
                        <span class="nav-item-text" x-show="!isSidebarCollapsed">Reports</span>
                        <div class="nav-item-indicator"></div>
                    </a>

                    <a href="{{ route('vendor.settings') }}"
                       class="nav-item {{ request()->routeIs('vendor.settings*') ? 'active' : '' }}"
                       title="Settings">
                        <div class="nav-item-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <span class="nav-item-text" x-show="!isSidebarCollapsed">Settings</span>
                        <div class="nav-item-indicator"></div>
                    </a>
                </div>
            </div>

            <!-- Support Section -->
            <div class="nav-section">
                <div class="nav-section-title" x-show="!isSidebarCollapsed">
                    <span>Support</span>
                </div>

                <div class="nav-items">
                    <a href="{{ route('vendor.support') }}"
                       class="nav-item {{ request()->routeIs('vendor.support*') ? 'active' : '' }}"
                       title="Help & Support">
                        <div class="nav-item-icon">
                            <i class="fas fa-life-ring"></i>
                        </div>
                        <span class="nav-item-text" x-show="!isSidebarCollapsed">Help & Support</span>
                        <div class="nav-item-indicator"></div>
                    </a>
                </div>
            </div>
        </nav>

        <!-- Modern Sidebar Footer -->
        <div class="sidebar-footer">
            <div class="user-profile" x-data="{ profileOpen: false }">
                <button @click="profileOpen = !profileOpen" class="user-profile-btn">
                    <div class="user-avatar">
                        <img src="https://ui-avatars.com/api/?name={{ urlencode(auth()->user()->name) }}&background=6366f1&color=fff"
                             alt="User avatar" class="user-avatar-img">
                        <div class="user-status-indicator"></div>
                    </div>
                    <div class="user-info" x-show="!isSidebarCollapsed">
                        <h4 class="user-name">{{ auth()->user()->name }}</h4>
                        <p class="user-role">Vendor Account</p>
                    </div>
                    <div class="user-dropdown-icon" x-show="!isSidebarCollapsed">
                        <i class="fas fa-chevron-up" :class="{ 'rotate-180': profileOpen }"></i>
                    </div>
                </button>

                <!-- User Dropdown Menu -->
                <div x-show="profileOpen && !isSidebarCollapsed"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 transform scale-95"
                     x-transition:enter-end="opacity-100 transform scale-100"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 transform scale-100"
                     x-transition:leave-end="opacity-0 transform scale-95"
                     class="user-dropdown-menu">
                    <a href="{{ route('vendor.profile') }}" class="dropdown-item">
                        <i class="fas fa-user"></i>
                        <span>My Profile</span>
                    </a>
                    <a href="{{ route('vendor.settings') }}" class="dropdown-item">
                        <i class="fas fa-cog"></i>
                        <span>Account Settings</span>
                    </a>
                    <div class="dropdown-divider"></div>
                    <form method="POST" action="{{ route('logout') }}" class="dropdown-form">
                        @csrf
                        <button type="submit" class="dropdown-item logout-btn">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>Sign Out</span>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </aside>

    <!-- Main Content Area -->
    <div class="vendor-main-content">
        <!-- Modern Header -->
        <header class="vendor-header">
            <div class="header-content">
                <!-- Left Section -->
                <div class="header-left">
                    <!-- Mobile Menu Toggle -->
                    <button @click="toggleMobileSidebar()" class="mobile-menu-btn mobile-only">
                        <i class="fas fa-bars"></i>
                    </button>

                    <!-- Page Title & Breadcrumb -->
                    <div class="page-title-section">
                        <h1 class="page-title">@yield('page-title', 'Dashboard')</h1>
                        <nav class="breadcrumb" x-show="window.innerWidth > 768">
                            <span class="breadcrumb-item">
                                <i class="fas fa-home"></i>
                                <span>Home</span>
                            </span>
                            <span class="breadcrumb-separator">
                                <i class="fas fa-chevron-right"></i>
                            </span>
                            <span class="breadcrumb-item current">
                                @yield('page-title', 'Dashboard')
                            </span>
                        </nav>
                    </div>
                </div>

                <!-- Center Section - Search -->
                <div class="header-center">
                    <div class="search-container" x-data="{ searchFocused: false, searchQuery: '' }">
                        <div class="search-input-wrapper" :class="{ 'focused': searchFocused }">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text"
                                   x-model="searchQuery"
                                   @focus="searchFocused = true"
                                   @blur="searchFocused = false"
                                   class="search-input"
                                   placeholder="Search products, orders, customers...">
                            <button x-show="searchQuery.length > 0"
                                    @click="searchQuery = ''"
                                    class="search-clear-btn">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <!-- Search Suggestions (can be populated with real data) -->
                        <div x-show="searchFocused && searchQuery.length > 2"
                             class="search-suggestions"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform scale-95"
                             x-transition:enter-end="opacity-100 transform scale-100">
                            <div class="search-suggestion-item">
                                <i class="fas fa-search"></i>
                                <span>Search for "<span x-text="searchQuery"></span>"</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Section -->
                <div class="header-right">
                    <!-- Quick Actions -->
                    <div class="quick-actions">
                        <button class="quick-action-btn" title="Add New Product" @click="$dispatch('open-modal', 'add-product')">
                            <i class="fas fa-plus"></i>
                        </button>

                        <button class="quick-action-btn" title="View Messages" @click="$dispatch('open-modal', 'messages')">
                            <i class="fas fa-envelope"></i>
                            <span class="action-badge">3</span>
                        </button>
                    </div>

                    <!-- Theme Toggle -->
                    <button @click="toggleDarkMode()" class="theme-toggle-btn" title="Toggle Dark Mode">
                        <div class="theme-toggle-track" :class="{ 'active': isDarkMode }">
                            <div class="theme-toggle-thumb">
                                <i class="fas fa-sun light-icon" :class="{ 'active': !isDarkMode }"></i>
                                <i class="fas fa-moon dark-icon" :class="{ 'active': isDarkMode }"></i>
                            </div>
                        </div>
                    </button>

                    <!-- Notifications -->
                    <div class="notifications-container" x-data="{ notificationsOpen: false }">
                        <button @click="notificationsOpen = !notificationsOpen"
                                class="notifications-btn"
                                title="Notifications">
                            <div class="notifications-icon">
                                <i class="fas fa-bell"></i>
                                <span class="notifications-count">5</span>
                                <div class="notifications-pulse"></div>
                            </div>
                        </button>

                        <!-- Notifications Dropdown -->
                        <div x-show="notificationsOpen"
                             @click.away="notificationsOpen = false"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform scale-95 translate-y-2"
                             x-transition:enter-end="opacity-100 transform scale-100 translate-y-0"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 transform scale-100 translate-y-0"
                             x-transition:leave-end="opacity-0 transform scale-95 translate-y-2"
                             class="notifications-dropdown">

                            <div class="notifications-header">
                                <h3 class="notifications-title">Notifications</h3>
                                <button class="mark-all-read-btn">Mark all as read</button>
                            </div>

                            <div class="notifications-list">
                                <div class="notification-item unread">
                                    <div class="notification-icon order">
                                        <i class="fas fa-shopping-cart"></i>
                                    </div>
                                    <div class="notification-content">
                                        <h4 class="notification-title">New Order Received</h4>
                                        <p class="notification-message">Order #WM-2024-001 has been placed by John Doe</p>
                                        <span class="notification-time">2 minutes ago</span>
                                    </div>
                                    <div class="notification-actions">
                                        <button class="notification-action-btn">View</button>
                                    </div>
                                </div>

                                <div class="notification-item">
                                    <div class="notification-icon message">
                                        <i class="fas fa-message"></i>
                                    </div>
                                    <div class="notification-content">
                                        <h4 class="notification-title">New Message</h4>
                                        <p class="notification-message">Customer inquiry about product availability</p>
                                        <span class="notification-time">1 hour ago</span>
                                    </div>
                                </div>

                                <div class="notification-item">
                                    <div class="notification-icon success">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="notification-content">
                                        <h4 class="notification-title">Payment Received</h4>
                                        <p class="notification-message">₹2,500 payment confirmed for Order #WM-2024-002</p>
                                        <span class="notification-time">3 hours ago</span>
                                    </div>
                                </div>
                            </div>

                            <div class="notifications-footer">
                                <a href="#" class="view-all-notifications">View All Notifications</a>
                            </div>
                        </div>
                    </div>

                    <!-- User Profile -->
                    <div class="user-profile-container" x-data="{ profileOpen: false }">
                        <button @click="profileOpen = !profileOpen" class="user-profile-btn">
                            <div class="user-avatar-wrapper">
                                <img src="https://ui-avatars.com/api/?name={{ urlencode(auth()->user()->name) }}&background=6366f1&color=fff"
                                     alt="User avatar" class="user-avatar-img">
                                <div class="user-status-dot"></div>
                            </div>
                            <div class="user-info desktop-only">
                                <span class="user-name">{{ auth()->user()->name }}</span>
                                <span class="user-role">Vendor</span>
                            </div>
                            <div class="user-dropdown-arrow desktop-only">
                                <i class="fas fa-chevron-down" :class="{ 'rotate-180': profileOpen }"></i>
                            </div>
                        </button>

                        <!-- User Profile Dropdown -->
                        <div x-show="profileOpen"
                             @click.away="profileOpen = false"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform scale-95 translate-y-2"
                             x-transition:enter-end="opacity-100 transform scale-100 translate-y-0"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 transform scale-100 translate-y-0"
                             x-transition:leave-end="opacity-0 transform scale-95 translate-y-2"
                             class="user-profile-dropdown">

                            <div class="profile-dropdown-header">
                                <div class="profile-avatar">
                                    <img src="https://ui-avatars.com/api/?name={{ urlencode(auth()->user()->name) }}&background=6366f1&color=fff"
                                         alt="User avatar">
                                </div>
                                <div class="profile-info">
                                    <h4 class="profile-name">{{ auth()->user()->name }}</h4>
                                    <p class="profile-email">{{ auth()->user()->email }}</p>
                                    <span class="profile-badge">Vendor Account</span>
                                </div>
                            </div>

                            <div class="profile-dropdown-menu">
                                <a href="{{ route('vendor.profile') }}" class="profile-menu-item">
                                    <i class="fas fa-user"></i>
                                    <span>My Profile</span>
                                </a>
                                <a href="{{ route('vendor.settings') }}" class="profile-menu-item">
                                    <i class="fas fa-cog"></i>
                                    <span>Account Settings</span>
                                </a>
                                <a href="#" class="profile-menu-item">
                                    <i class="fas fa-credit-card"></i>
                                    <span>Billing & Plans</span>
                                </a>
                                <a href="{{ route('vendor.support') }}" class="profile-menu-item">
                                    <i class="fas fa-life-ring"></i>
                                    <span>Help & Support</span>
                                </a>

                                <div class="profile-menu-divider"></div>

                                <form method="POST" action="{{ route('logout') }}" class="profile-logout-form">
                                    @csrf
                                    <button type="submit" class="profile-menu-item logout-item">
                                        <i class="fas fa-sign-out-alt"></i>
                                        <span>Sign Out</span>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="vendor-main">
            <!-- Page Header Actions -->
            <div class="page-header-actions" x-show="$store.pageActions && $store.pageActions.length > 0">
                @yield('header-actions')
            </div>

            <!-- Page Content -->
            <div class="page-content">
                @yield('content')
                @yield('dashboard-content')
            </div>
        </main>
    </div>
</div>

<!-- Global Modals -->
<div id="global-modals">
    @stack('modals')
</div>

<!-- Toast Notifications -->
<div id="toast-container" class="toast-container">
    @stack('toasts')
</div>

<!-- Page Scripts -->
@stack('scripts')

<!-- Vendor Layout JavaScript -->
<script>
function vendorLayout() {
    return {
        // State Management
        isLoading: true,
        isDarkMode: localStorage.getItem('vendor-dark-mode') === 'true',
        isSidebarCollapsed: localStorage.getItem('vendor-sidebar-collapsed') === 'true',
        isMobileSidebarOpen: false,

        // Initialization
        init() {
            // Initialize theme
            this.applyTheme();

            // Hide loading screen after initialization
            setTimeout(() => {
                this.isLoading = false;
            }, 1000);

            // Handle window resize
            window.addEventListener('resize', () => {
                if (window.innerWidth > 1024) {
                    this.isMobileSidebarOpen = false;
                }
            });

            // Initialize animations
            this.initAnimations();
        },

        // Theme Management
        toggleDarkMode() {
            this.isDarkMode = !this.isDarkMode;
            localStorage.setItem('vendor-dark-mode', this.isDarkMode);
            this.applyTheme();
        },

        applyTheme() {
            if (this.isDarkMode) {
                document.documentElement.classList.add('dark');
                document.body.classList.add('dark-mode');
            } else {
                document.documentElement.classList.remove('dark');
                document.body.classList.remove('dark-mode');
            }
        },

        // Sidebar Management
        toggleSidebarCollapse() {
            this.isSidebarCollapsed = !this.isSidebarCollapsed;
            localStorage.setItem('vendor-sidebar-collapsed', this.isSidebarCollapsed);
        },

        toggleMobileSidebar() {
            this.isMobileSidebarOpen = !this.isMobileSidebarOpen;
        },

        closeMobileSidebar() {
            this.isMobileSidebarOpen = false;
        },

        // Navigation
        navigateToHome() {
            window.location.href = '{{ route("vendor.dashboard") }}';
        },

        // Animations
        initAnimations() {
            // Animate sidebar items on load
            if (typeof gsap !== 'undefined') {
                gsap.from('.nav-item', {
                    duration: 0.6,
                    y: 20,
                    opacity: 0,
                    stagger: 0.1,
                    ease: 'power2.out',
                    delay: 0.5
                });
            }
        }
    }
}

// Global notification system
window.showToast = function(message, type = 'info', duration = 5000) {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="toast-icon fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span class="toast-message">${message}</span>
            <button class="toast-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    document.getElementById('toast-container').appendChild(toast);

    // Auto remove after duration
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, duration);
};

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});
</script>

<!-- Chart Initialization -->
@stack('scripts-charts')

</body>
</html>